"""Define a ReAct agent using LangGraph.

This agent follows the ReAct pattern (Reasoning and Acting) to solve tasks
by thinking step by step and using tools when needed.
"""

import functools
import operator
from typing import Annotated, Literal, Sequence, TypedDict
from dotenv import load_dotenv
from langchain_openai import Chat<PERSON>penAI
from langgraph.graph import StateGraph, END

from langchain_core.messages import AIMessage, HumanMessage, SystemMessage, BaseMessage
from langchain_core.prompts import Chat<PERSON>romptTemplate, MessagesPlaceholder
from pydantic import BaseModel

from bond_ai.configuration import Configuration
from bond_ai.state import  BondAIAgentState
from bond_ai.registry import agent_registry
from bond_ai.utils import load_chat_model

from langgraph.prebuilt import create_react_agent

load_dotenv()



# Import and register tools
from langchain_community.tools.tavily_search import TavilySearchResults
from langchain_experimental.tools import PythonREPLTool

tavily_tool = TavilySearchResults(max_results=5)
python_repl_tool = PythonREPLTool()

agent_registry.register_tool("tavily_search", tavily_tool)
agent_registry.register_tool("python_repl", python_repl_tool)

# Register agents using the simple registry
agent_registry.register_agent(
    "Researcher",
    "You are a research expert. Find accurate, relevant information and cite sources.",
    [tavily_tool]
).register_agent(
    "Coder",
    "You are a Python coding expert. Write clean, efficient code and explain your approach.",
    [python_repl_tool]
)

# Example: Register existing node functions
try:
    from bond_ai.nodes.table_indexing_node import table_indexing_node
    agent_registry.register_node(
        "TableIndexer",
        table_indexing_node,
        description="Indexes table data and creates summaries for the agent"
    )
    print("✓ Registered TableIndexer node")
except ImportError as e:
    print(f"⚠ Could not import table_indexing_node: {e}")

try:
    from  bond_ai.nodes.planner_node import planner_agent
    agent_registry.register_node(
        "Planner",
        planner_agent,
        description="Creates comprehensive plans for complex tasks"
    )
    print("✓ Registered Planner node")
except ImportError as e:
    print(f"⚠ Could not import planner_agent: {e}")




# Get members from registry for backward compatibility
members = agent_registry.get_agent_names()
 

    
def agent_node(state, agent, name):
    result = agent.invoke(state)
    return {
        "messages": [HumanMessage(content=result["messages"][-1].content, name=name)]
    }




system_prompt_supervisor = (
    "You are a supervisor tasked with managing a conversation between the"
    " following workers: {members}. Given the following user request,"
    " respond with the worker to act next. Each worker will perform a"
    " task and respond with their results and status. When finished," 
    " respond with FINISH." )

options = ["FINISH"] + members

class routeResponse(BaseModel):
    next: Literal[*options] # type: ignore
    
    
    

prompt = ChatPromptTemplate.from_messages(
    [
        ("system", system_prompt_supervisor),
        MessagesPlaceholder(variable_name="messages"),
        (
            "system",
            "Given the conversation above, who should act next?"
            "Or should we FINISH? Select one of {options}"
        ),
    ]
).partial(options=str(options), members=", ".join(members))

llm = load_chat_model("bedrock/us.anthropic.claude-3-7-sonnet-20250219-v1:0")
 

def supervisor_agent(state):
    supervisor_chain = prompt | llm.with_structured_output(routeResponse)
    return supervisor_chain.invoke(state)


def supervisor_agent_anthropic(state):
    """Alternative supervisor agent implementation without structured output.

    This version manually parses the LLM response instead of using
    llm.with_structured_output(routeResponse) for better compatibility
    with different LLM providers.
    """
    import re
    import json

    # Create a modified prompt that asks for JSON response
    json_prompt = ChatPromptTemplate.from_messages([
        ("system", system_prompt_supervisor),
        MessagesPlaceholder(variable_name="messages"),
        (
            "system",
            "Given the conversation above, who should act next? "
            "Or should we FINISH? Select one of {options}. "
            "Respond with a JSON object in the format: {{\"next\": \"AGENT_NAME\"}} "
            "where AGENT_NAME is one of: {options}"
        ),
    ]).partial(options=str(options), members=", ".join(members))

    # Get response from LLM
    chain = json_prompt | llm
    response = chain.invoke(state)

    # Extract the content from the response
    if hasattr(response, 'content'):
        content = response.content
    else:
        content = str(response)

    # Try to parse JSON from the response
    try:
        # Look for JSON in the response
        json_match = re.search(r'\{[^}]*"next"[^}]*\}', content)
        if json_match:
            json_str = json_match.group()
            parsed = json.loads(json_str)
            next_agent = parsed.get("next")
        else:
            # Fallback: look for any of the valid options in the response
            next_agent = None
            for option in options:
                if option.upper() in content.upper():
                    next_agent = option
                    break

            if next_agent is None:
                next_agent = "FINISH"  # Default fallback

    except (json.JSONDecodeError, AttributeError):
        # Fallback parsing: look for any valid option in the response
        next_agent = None
        for option in options:
            if option.upper() in content.upper():
                next_agent = option
                break

        if next_agent is None:
            next_agent = "FINISH"  # Default fallback

    # Validate the choice
    if next_agent not in options:
        next_agent = "FINISH"  # Safe fallback

    # Return in the same format as the original function
    return {"next": next_agent}


def _create_react_agent(name: str, system_prompt: str, tools: list, llm=None):
    """Factory function to create a ReAct agent with system prompt and tools."""
    if llm is None:
        llm = ChatOpenAI(model="gpt-4o-mini")

    prompt = ChatPromptTemplate.from_messages([
        ("system", system_prompt),
        MessagesPlaceholder(variable_name="messages")
    ])

    agent = create_react_agent(llm, tools=tools, state_modifier=prompt)
    return functools.partial(agent_node, agent=agent, name=name)


def create_workflow_from_registry():
    """Create workflow using the agent registry."""
    workflow = StateGraph(BondAIAgentState)
    # Create ReAct agents from registry
    react_agents = agent_registry.get_react_agents()
    for name, config in react_agents.items():
        # Filter config to only include parameters that _create_react_agent expects
        agent_params = {
            "name": name,
            "system_prompt": config["system_prompt"],
            "tools": config["tools"]
        }
        # Add llm if specified in config
        if "llm" in config:
            agent_params["llm"] = config["llm"]

        node = _create_react_agent(**agent_params)
        workflow.add_node(name, node)
        print(f"✓ Added ReAct agent: {name}")

    # Add custom nodes from registry
    custom_nodes = agent_registry.get_custom_nodes()
    for name, config in custom_nodes.items():
        node_function = config["node_function"]
        workflow.add_node(name, node_function)
        print(f"✓ Added custom node: {name}")

    # Add supervisor
    workflow.add_node("supervisor", supervisor_agent_anthropic)

    # Connect all agents/nodes to supervisor
    current_members = agent_registry.get_agent_names()
    for member in current_members:
        workflow.add_edge(member, "supervisor")

    # Create conditional routing
    conditional_map = {k: k for k in current_members}
    conditional_map["FINISH"] = END
    workflow.add_conditional_edges("supervisor", lambda x: x["next"], conditional_map)

    workflow.set_entry_point("supervisor")
    return workflow.compile()


# ============================================================================
# UTILITY FUNCTIONS FOR EASY AGENT MANAGEMENT
# ============================================================================

def add_agent(name: str, system_prompt: str, tools: list, enabled: bool = True):
    """Add a new ReAct agent to the registry."""
    agent_registry.register_agent(name, system_prompt, tools, enabled)
    print(f"✓ Added ReAct agent: {name}")

def add_node(name: str, node_function, enabled: bool = True, **kwargs):
    """Add a pre-built node function to the registry."""
    agent_registry.register_node(name, node_function, enabled, **kwargs)
    print(f"✓ Added custom node: {name}")

def enable_agent(name: str):
    """Enable an agent or node."""
    agent_registry.enable_agent(name)
    print(f"✓ Enabled: {name}")

def disable_agent(name: str):
    """Disable an agent or node."""
    agent_registry.disable_agent(name)
    print(f"✓ Disabled: {name}")

def list_agents():
    """List all agents and nodes with their status."""
    print("\n=== AGENT REGISTRY ===")
    for name, config in agent_registry.agents.items():
        status = "✓ ENABLED" if config.get("enabled", True) else "✗ DISABLED"
        agent_type = config.get("type", "unknown")

        if agent_type == "react_agent":
            tools_count = len(config.get("tools", []))
            print(f"{status} | {name} | Type: ReAct Agent | Tools: {tools_count}")
        elif agent_type == "custom_node":
            print(f"{status} | {name} | Type: Custom Node | Function: {config.get('node_function', {}).get('__name__', 'Unknown')}")
        else:
            print(f"{status} | {name} | Type: {agent_type}")
    print()

def rebuild_workflow():
    """Rebuild the workflow with current registry state."""
    global graph, members
    members = agent_registry.get_agent_names()
    graph = create_workflow_from_registry()
    print("✓ Workflow rebuilt")
    return graph

# Create the initial workflow
graph = create_workflow_from_registry()


# ============================================================================
# EXAMPLE USAGE OF THE FLEXIBLE AGENT REGISTRY
# ============================================================================

"""
Example of how to use the flexible agent registry:

# 1. Add a new ReAct agent
add_agent(
    "DataAnalyst",
    "You are a data analysis expert. Analyze data and provide insights.",
    [python_repl_tool, tavily_tool]
)

# 2. Add a pre-built node function from imports
from bond_ai.nodes.table_indexing_node import table_indexing_node
add_node("TableIndexer", table_indexing_node, description="Indexes table data and creates summaries")

# 3. Add another existing node
from bond_ai.nodes.planner_node import planner_agent
add_node("Planner", planner_agent, description="Creates plans for complex tasks")

# 4. Disable an existing agent
disable_agent("Coder")

# 5. List all agents and nodes
list_agents()

# 6. Rebuild workflow with new configuration
rebuild_workflow()

# 7. Add a custom tool and agent
from langchain_core.tools import tool

@tool
def custom_calculator(expression: str) -> str:
    \"\"\"Calculate mathematical expressions.\"\"\"
    try:
        result = eval(expression)
        return f"Result: {result}"
    except:
        return "Error in calculation"

agent_registry.register_tool("calculator", custom_calculator)

add_agent(
    "MathExpert",
    "You are a mathematics expert. Solve mathematical problems step by step.",
    [custom_calculator]
)

# 8. Create a custom node function and register it
def custom_validator_node(state, config):
    \"\"\"Custom node that validates data.\"\"\"
    # Your custom logic here
    return {"messages": [{"content": "Data validated successfully"}]}

add_node("DataValidator", custom_validator_node, description="Validates data integrity")

rebuild_workflow()
"""
